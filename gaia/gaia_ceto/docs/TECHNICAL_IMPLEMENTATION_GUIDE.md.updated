# Multi-MCP Technical Implementation Guide

This document provides detailed technical information about the current multi-MCP implementation, including class structures, key methods, and implementation patterns.

## Core Classes and Architecture

### 1. MCPServerMulti (`mcp_http_server_multi.py`)

The main server class that orchestrates the entire system.

#### Key Components

```python
class MCPServerMulti:
    def __init__(self, config_file: str = "server_config.json"):
        self.config_file = config_file
        self.third_party_client: Optional[MultiMCPClient] = None
        self.server_configs: Dict[str, Dict[str, Any]] = {}
        self.global_settings: Dict[str, Any] = {}
        self._initialized = False
        self._cleanup_lock = asyncio.Lock()
        self._connection_retry_counts: Dict[str, int] = {}
        self._max_retries = 3
        self._retry_delay = 5.0
        
        # Initialize FastMCP with proper lifespan management
        self.mcp = FastMCP("gaia_aggregate_server", lifespan=self.lifespan)
```

#### Lifespan Management

```python
@asynccontextmanager
async def lifespan(self, app):
    """Proper async lifespan management for FastMCP."""
    logger.info("Starting server lifespan...")
    try:
        await self.initialize()
        yield
    finally:
        logger.info("Cleaning up server lifespan...")
        await self.cleanup()
```

#### Tool Registration

```python
def _register_builtin_tools(self):
    """Register built-in local tools."""
    self.mcp.tool()(echostring)
    self.mcp.tool()(echostring_table)
    self.mcp.tool()(long_task)
    self.mcp.tool()(firecrawl_scrape_text_only)
    self.mcp.tool()(self.server_status)
    self.mcp.tool()(self.list_all_tools)
    self.mcp.tool()(self.third_party_health)
```

#### Third-party Tool Delegation

```python
async def _register_third_party_tool(self, server_id: str, namespace: str, tool_info: Dict[str, Any]):
    """Register a third-party tool with proper delegation."""
    tool_name = tool_info['name']
    namespaced_name = self._create_namespaced_tool_name(namespace, tool_name)
    
    # Create wrapper function for delegation
    async def tool_wrapper(**kwargs):
        return await self._delegate_tool_call(server_id, tool_name, kwargs)
    
    # Set function metadata
    tool_wrapper.__name__ = namespaced_name
    tool_wrapper.__doc__ = tool_info.get('description', f'Delegated tool: {tool_name}')
    
    # Register with FastMCP
    self.mcp.tool()(tool_wrapper)
```

### 2. MultiMCPClient (`mcp_client_multi.py`)

The client manager that handles connections to multiple backend servers.

#### Architecture Components

```python
class MultiMCPClient:
    def __init__(self, anthropic_api_key: Optional[str] = None, default_timeout: float = 120.0):
        self.anthropic_api_key = anthropic_api_key or os.getenv('ANTHROPIC_API_KEY')
        self.default_timeout = default_timeout
        self.connection_manager = ConnectionManager()
        self.tool_router = ToolRouter(self.connection_manager)
        
        # Backward compatibility
        self.connections: Dict[str, Dict[str, Any]] = {}
        self._update_lock = asyncio.Lock()
```

#### Connection Manager

```python
class ConnectionManager:
    """Manages MCP server connections."""
    
    def __init__(self):
        self._connections: Dict[str, ServerInfo] = {}
        self._lock = asyncio.Lock()
    
    async def add_connection(self, server_id: str, url: str, protocol: Protocol, **kwargs) -> bool:
        """Add a new server connection."""
        async with self._lock:
            if server_id in self._connections:
                logger.warning(f"Server {server_id} already exists")
                return False
            
            try:
                client = ClientFactory.create_client(protocol, **kwargs)
                success = await client.connect(url)
                
                if success:
                    server_info = ServerInfo(
                        server_id=server_id,
                        url=url,
                        protocol=protocol,
                        client=client,
                        description=kwargs.get('description', ''),
                        connected_at=datetime.now(),
                        tools=client.available_tools
                    )
                    self._connections[server_id] = server_info
                    logger.info(f"Successfully connected to {server_id}")
                    return True
                    
            except Exception as e:
                logger.error(f"Failed to connect to {server_id}: {e}")
                return False
```

#### Tool Router

```python
class ToolRouter:
    """Routes tool calls to appropriate servers."""
    
    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
    
    async def route_tool_call(self, server_id: str, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Route a tool call to the specified server."""
        server_info = await self.connection_manager.get_connection(server_id)
        if not server_info:
            raise ValueError(f"Server {server_id} not found")
        
        if not server_info.client.is_connected:
            raise ConnectionError(f"Server {server_id} is not connected")
        
        return await server_info.client.call_tool(tool_name, arguments)
```

#### Protocol Abstraction

```python
class MCPClientInterface(ABC):
    """Abstract interface for MCP clients."""
    
    @property
    @abstractmethod
    def is_connected(self) -> bool:
        """Check if client is connected."""
        pass
    
    @property
    @abstractmethod
    def available_tools(self) -> List[Dict[str, Any]]:
        """Get list of available tools."""
        pass
    
    @abstractmethod
    async def connect(self, url: str) -> bool:
        """Connect to MCP server."""
        pass
    
    @abstractmethod
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Call a tool on the server."""
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """Disconnect from server."""
        pass
```

#### Client Factory

```python
class ClientFactory:
    """Factory for creating MCP clients based on protocol."""
    
    @staticmethod
    def create_client(protocol: Protocol, **kwargs) -> MCPClientInterface:
        """Create appropriate client for the given protocol."""
        if protocol == Protocol.HTTP:
            return HTTPMCPClient(kwargs.get('anthropic_api_key'))
        elif protocol == Protocol.SSE:
            return SSEMCPClient(kwargs.get('anthropic_api_key'))
        elif protocol == Protocol.STDIO:
            return StdioMCPClient(
                command=kwargs['command'],
                args=kwargs.get('args', []),
                env=kwargs.get('env', {})
            )
        else:
            raise ValueError(f"Unsupported protocol: {protocol}")
```

## Configuration System

### Configuration Loading

```python
async def load_configuration(self):
    """Load and validate server configuration."""
    if not os.path.exists(self.config_file):
        logger.warning(f"Configuration file {self.config_file} not found. Using defaults.")
        return
    
    try:
        with open(self.config_file, 'r') as f:
            config = json.load(f)
        
        self.global_settings = config.get('globalSettings', {})
        self.server_configs = config.get('mcpServers', {})
        
        logger.info(f"Loaded configuration for {len(self.server_configs)} servers")
        
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        raise
```

### Environment Variable Resolution

```python
def _resolve_env_variables(self, value: str) -> str:
    """Resolve environment variables in configuration values."""
    if isinstance(value, str) and '{' in value and '}' in value:
        import re
        pattern = r'\{([^}]+)\}'
        
        def replace_env_var(match):
            env_var = match.group(1)
            env_value = os.getenv(env_var)
            if env_value is None:
                logger.warning(f"Environment variable {env_var} not found")
                return match.group(0)  # Return original if not found
            return env_value
        
        return re.sub(pattern, replace_env_var, value)
    return value
```

## Parameter Processing

### Parameter Mapping

```python
def _apply_parameter_mapping(self, server_id: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Apply parameter mapping for a specific tool."""
    if server_id not in self.server_configs:
        return arguments
    
    config = self.server_configs[server_id]
    parameter_mapping = config.get('parameterMapping', {}).get(tool_name, {})
    
    if not parameter_mapping:
        return arguments
    
    mapped_arguments = {}
    for key, value in arguments.items():
        mapped_key = parameter_mapping.get(key, key)
        mapped_arguments[mapped_key] = value
    
    logger.debug(f"Applied parameter mapping for {tool_name}: {arguments} -> {mapped_arguments}")
    return mapped_arguments
```

### Default Parameters

```python
def _apply_default_parameters(self, server_id: str, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
    """Apply default parameters for a specific tool."""
    if server_id not in self.server_configs:
        return arguments
    
    config = self.server_configs[server_id]
    default_parameters = config.get('defaultParameters', {}).get(tool_name, {})
    
    if not default_parameters:
        return arguments
    
    # Apply defaults only for missing parameters
    final_arguments = default_parameters.copy()
    final_arguments.update(arguments)  # User arguments override defaults
    
    logger.debug(f"Applied default parameters for {tool_name}: {arguments} -> {final_arguments}")
    return final_arguments
```

## Error Handling and Recovery

### Connection Retry Logic

```python
async def _connect_server_with_retry(self, server_id: str, config: Dict[str, Any]) -> bool:
    """Connect to a server with retry logic."""
    max_retries = self._max_retries
    retry_count = self._connection_retry_counts.get(server_id, 0)
    
    if retry_count >= max_retries:
        logger.error(f"Max retries ({max_retries}) exceeded for {server_id}")
        return False
    
    try:
        success = await self._connect_single_server(server_id, config)
        if success:
            self._connection_retry_counts[server_id] = 0  # Reset on success
            return True
        else:
            self._connection_retry_counts[server_id] = retry_count + 1
            logger.warning(f"Connection failed for {server_id}, retry {retry_count + 1}/{max_retries}")
            return False
            
    except Exception as e:
        self._connection_retry_counts[server_id] = retry_count + 1
        logger.error(f"Error connecting to {server_id}: {e}")
        return False
```

### Graceful Degradation

```python
async def _delegate_tool_call(self, server_id: str, tool_name: str, arguments: Dict[str, Any]) -> Any:
    """Delegate tool call to third-party server with error handling."""
    if not self.third_party_client:
        raise RuntimeError("Third-party client not initialized")
    
    try:
        # Apply parameter transformations
        mapped_args = self._apply_parameter_mapping(server_id, tool_name, arguments)
        final_args = self._apply_default_parameters(server_id, tool_name, mapped_args)
        
        # Make the call
        result = await self.third_party_client.call_tool(server_id, tool_name, final_args)
        return result
        
    except Exception as e:
        logger.error(f"Error calling {tool_name} on {server_id}: {e}")
        return {
            "error": f"Tool call failed: {str(e)}",
            "server_id": server_id,
            "tool_name": tool_name
        }
```

## Monitoring and Debugging

### Server Status Tool

```python
async def server_status(self) -> Dict[str, Any]:
    """Get comprehensive server status."""
    status = {
        "server_name": "Gaia Aggregate MCP Server",
        "version": "2.0",
        "initialized": self._initialized,
        "local_tools": len([tool for tool in self.mcp._tools.keys() if not any(ns in tool for ns in ['__'])]),
        "third_party_servers": {},
        "global_settings": self.global_settings
    }
    
    if self.third_party_client:
        connections = await self.third_party_client.get_all_connections()
        for server_id, conn_info in connections.items():
            status["third_party_servers"][server_id] = {
                "connected": conn_info.get('connected', False),
                "url": conn_info.get('url', 'unknown'),
                "protocol": conn_info.get('protocol', 'unknown'),
                "tools_count": len(conn_info.get('tools', [])),
                "description": conn_info.get('description', ''),
                "connected_at": conn_info.get('connected_at', 'unknown')
            }
    
    return status
```

### Health Check Tool

```python
async def third_party_health(self) -> Dict[str, Any]:
    """Check health of all third-party connections."""
    if not self.third_party_client:
        return {"error": "Third-party client not initialized"}
    
    health_status = {}
    connections = await self.third_party_client.get_all_connections()
    
    for server_id, conn_info in connections.items():
        try:
            # Attempt a simple health check
            is_healthy = conn_info.get('connected', False)
            health_status[server_id] = {
                "healthy": is_healthy,
                "status": "connected" if is_healthy else "disconnected",
                "tools_available": len(conn_info.get('tools', [])),
                "last_check": datetime.now().isoformat()
            }
        except Exception as e:
            health_status[server_id] = {
                "healthy": False,
                "status": f"error: {str(e)}",
                "tools_available": 0,
                "last_check": datetime.now().isoformat()
            }
    
    return {
        "overall_health": all(status["healthy"] for status in health_status.values()),
        "servers": health_status,
        "total_servers": len(health_status),
        "healthy_servers": sum(1 for status in health_status.values() if status["healthy"])
    }
```

## Usage Patterns

### Starting the Server

```python
def main():
    """Main entry point with argument parsing."""
    parser = argparse.ArgumentParser(description="Gaia Aggregate MCP HTTP Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=9000, help="Port to bind to")
    parser.add_argument("--config", default="server_config.json", help="Configuration file")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    server = MCPServerMulti(args.config)
    
    try:
        asyncio.run(server.run_server(args.host, args.port))
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server failed: {e}")
        sys.exit(1)
```

This technical implementation provides a robust, scalable foundation for multi-MCP server integration with proper error handling, monitoring, and debugging capabilities.
