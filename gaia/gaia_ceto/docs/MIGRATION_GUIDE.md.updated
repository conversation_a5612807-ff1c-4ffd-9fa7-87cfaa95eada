# Migration Guide: Enhanced Server to Multi-MCP Server

This guide helps you migrate from the previous enhanced MCP server implementation to the new multi-MCP server architecture.

## Overview of Changes

The new implementation (`mcp_http_server_multi.py` + `mcp_client_multi.py`) provides significant improvements over the previous enhanced server:

### Key Improvements

1. **Resolved Async Context Issues**: Proper FastMCP lifespan management eliminates server crashes
2. **Clean Architecture**: Separate classes for different concerns (ConnectionManager, ToolRouter, etc.)
3. **Protocol Abstraction**: Unified interface for HTTP, SSE, and stdio protocols
4. **Enhanced Error Recovery**: Comprehensive error handling and automatic reconnection
5. **Better Connection Management**: Centralized lifecycle management and health monitoring

## File Changes

### Previous Implementation
- `mcp_http_server_enhanced.py` - Main server with embedded client logic
- Various client libraries imported directly

### New Implementation
- `mcp_http_server_multi.py` - Clean server implementation with proper lifespan
- `mcp_client_multi.py` - Dedicated multi-MCP client with clean architecture

## Configuration Compatibility

**Good News**: Your existing `server_config.json` files are fully compatible with the new implementation.

### Configuration Format (Unchanged)
```json
{
    "description": "Enhanced MCP Server Configuration (Augment Pattern)",
    "globalSettings": {
        "defaultTimeout": 120,
        "longRunningToolTimeout": 300
    },
    "mcpServers": {
        "firecrawl-mcp": {
            "enabled": true,
            "command": "npx",
            "args": ["-y", "firecrawl-mcp"],
            "env": {
                "FIRECRAWL_API_KEY": "{FIRECRAWL_API_KEY}"
            },
            "namespace": "fc",
            "parameterMapping": {
                "firecrawl_search": {
                    "q": "query"
                }
            },
            "defaultParameters": {
                "firecrawl_scrape": {
                    "formats": ["markdown"],
                    "onlyMainContent": true
                }
            }
        }
    }
}
```

## Migration Steps

### 1. Stop Current Server

```bash
# Stop your current enhanced server
# (Ctrl+C if running in terminal, or kill the process)
```

### 2. Update Server Command

```bash
# Before (enhanced server)
python mcp_http_server_enhanced.py --port 9000 --config server_config.json

# After (multi-MCP server)
python mcp_http_server_multi.py --port 9000 --config server_config.json
```

### 3. No Client Changes Required

Your `chat_term` connection remains exactly the same:

```bash
python -m gaia.gaia_ceto.ceto_v002.chat_term \
  --llm mcp-http \
  --mcp-http-server http://localhost:9000/mcp
```

### 4. Verify Migration

After starting the new server, test that everything works:

```bash
# In chat_term, try these commands:
> Use server_status to check server health
> Use list_all_tools to see available tools
> Use third_party_health to verify backend connections
> Test a third-party tool like fc__firecrawl_scrape
```

## Behavioral Changes

### Improved Stability

**Before**: Server could crash after third-party tool calls due to async context issues
```
ERROR: Server crashed after successful tool execution
```

**After**: Stable operation with proper async lifespan management
```
INFO: Tool call completed successfully, server remains stable
```

### Enhanced Error Handling

**Before**: Limited error recovery, manual restart often required
```
ERROR: Connection lost to backend server
[Server becomes unresponsive]
```

**After**: Automatic reconnection and graceful degradation
```
WARNING: Connection lost to backend server, attempting reconnection...
INFO: Successfully reconnected to backend server
```

### Better Logging

**Before**: Basic logging with limited context
```
INFO: Tool call completed
```

**After**: Comprehensive logging with detailed context
```
INFO: Tool fc__firecrawl_scrape completed successfully
DEBUG: Applied parameter mapping: q -> query
DEBUG: Applied default parameters: formats=["markdown"]
```

## Tool Compatibility

### Tool Names (Unchanged)
All tool names remain the same:
- Local tools: `echostring`, `long_task`, `server_status`
- Third-party tools: `fc__firecrawl_scrape`, `search__brave_web_search`

### Tool Functionality (Enhanced)
- Same functionality with improved reliability
- Better error messages and debugging information
- More stable execution for long-running tasks

## Troubleshooting Migration Issues

### Issue: Server Won't Start

**Symptoms**:
```
ERROR: Failed to start server
```

**Solutions**:
1. Check if port 9000 is available: `lsof -i :9000`
2. Verify configuration file syntax: `python -m json.tool server_config.json`
3. Check environment variables are set: `echo $FIRECRAWL_API_KEY`
4. Enable debug logging: `--debug` flag

### Issue: Third-party Tools Not Available

**Symptoms**:
```
> Use fc__firecrawl_scrape
Tool not found: fc__firecrawl_scrape
```

**Solutions**:
1. Check server status: Use `server_status` tool
2. Verify backend health: Use `third_party_health` tool
3. Check configuration: Ensure `enabled: true` for the server
4. Verify environment variables: Check API keys are set
5. Check logs for connection errors

### Issue: Parameter Mapping Not Working

**Symptoms**:
```
ERROR: Invalid parameter 'q' for tool
```

**Solutions**:
1. Verify parameter mapping in configuration:
   ```json
   "parameterMapping": {
     "firecrawl_search": {
       "q": "query"
     }
   }
   ```
2. Check tool schema with `list_all_tools`
3. Enable debug logging to see parameter transformations

### Issue: Performance Degradation

**Symptoms**:
- Slower tool execution
- Timeout errors

**Solutions**:
1. Check global timeout settings in configuration
2. Use `third_party_health` to identify slow connections
3. Monitor server logs for performance issues
4. Consider adjusting timeout values:
   ```json
   "globalSettings": {
     "defaultTimeout": 180,
     "longRunningToolTimeout": 600
   }
   ```

## Rollback Plan

If you encounter issues with the new implementation, you can temporarily rollback:

### 1. Stop New Server
```bash
# Stop multi-MCP server
```

### 2. Start Previous Server
```bash
# Start enhanced server
python mcp_http_server_enhanced.py --port 9000 --config server_config.json
```

### 3. Report Issues
Please report any migration issues with:
- Error logs from both implementations
- Configuration file used
- Steps to reproduce the issue

## Benefits After Migration

### 1. Stability
- No more server crashes after tool calls
- Proper async context management
- Stable long-running operations

### 2. Reliability
- Automatic reconnection to backend servers
- Graceful error handling and recovery
- Better connection lifecycle management

### 3. Maintainability
- Clean, modular architecture
- Comprehensive logging and debugging
- Easier to extend and modify

### 4. Performance
- Improved connection pooling
- Better resource management
- Optimized tool routing

## Advanced Migration Considerations

### Custom Tool Integration

If you have custom tools integrated with the previous server:

1. **Local Tools**: Register them the same way with `self.mcp.tool()`
2. **Third-party Integration**: Use the new `MultiMCPClient` interface
3. **Parameter Processing**: Leverage the enhanced parameter mapping system

### Configuration Enhancements

Consider taking advantage of new configuration features:

```json
{
  "globalSettings": {
    "defaultTimeout": 120,
    "longRunningToolTimeout": 300,
    "maxRetries": 3,
    "retryDelay": 5.0
  },
  "mcpServers": {
    "your-server": {
      "enabled": true,
      "description": "Detailed description for monitoring",
      "toolTimeouts": {
        "specific_tool": 600
      }
    }
  }
}
```

### Monitoring Integration

The new implementation provides better monitoring capabilities:

- Use `server_status` for comprehensive health checks
- Use `third_party_health` for backend monitoring
- Enable debug logging for detailed operation tracking
- Monitor connection retry patterns and success rates

## Conclusion

The migration to the new multi-MCP server implementation provides significant stability and reliability improvements while maintaining full backward compatibility. The enhanced error handling, proper async management, and clean architecture make it a robust foundation for production use.

Most users will experience a seamless migration with improved stability and better error recovery. The same configuration files and client connections work without modification, making the upgrade process straightforward.
