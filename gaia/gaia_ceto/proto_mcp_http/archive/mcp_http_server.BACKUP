#!/usr/bin/env python3
"""
MCP HTTP Server

This module implements an MCP server using the streamable HTTP protocol.
It provides the same functionality as the SSE version but uses HTTP streaming.
"""

#from fastmcp import FastMCP, Context
from mcp.server.fastmcp import FastMCP, Context
import asyncio
from pydantic import Field
import argparse
import uvicorn

# Import shared tools from the common module
from gaia.gaia_ceto.proto_mcp.mcp_tools import (
    echostring,
    echostring_table,
    long_task,
    firecrawl_scrape,
    firecrawl_scrape_text_only,
    get_company_categories_matching,
    get_llm_completion,
    get_top_companies,
    get_company_listing,
    org_frame_cols
)

# Initialize FastMCP server
mcp = FastMCP("gaia_mcp_http_server")


# Register the shared tools with the MCP server using add_tool method
mcp.add_tool(echostring)
mcp.add_tool(echostring_table)
mcp.add_tool(long_task)
#mcp.add_tool(firecrawl_scrape)
mcp.add_tool(firecrawl_scrape_text_only)
#mcp.add_tool(get_company_categories_matching)
#mcp.add_tool(get_llm_completion)
#mcp.add_tool(get_top_companies)
#mcp.add_tool(get_company_listing)


if __name__ == "__main__":
    # Initialize and run the server
    parser = argparse.ArgumentParser()
    parser.add_argument("--port", type=int, default=9000, help="HTTP port")
    args = parser.parse_args()

    # Create the app using streamable_http_app for HTTP streaming
    app = mcp.streamable_http_app()

    uvicorn.run(app, host="0.0.0.0", port=args.port, log_level="info")
