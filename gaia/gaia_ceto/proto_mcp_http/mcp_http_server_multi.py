#!/usr/bin/env python3
"""
Gaia Aggregate MCP HTTP Server - Final clean implementation with improved SSE handling

A production-ready MCP server that supports:
- Local tools with full FastMCP features
- Third-party MCP servers (URL-based and process-spawned)
- Parameter mapping and default parameters
- SSE and HTTP protocols with robust error handling
- Clean error handling and logging
- Stable async context management
- Automatic retry for failed connections
"""

import os
import sys
import json
import asyncio
import logging
import argparse
import traceback
from typing import Dict, List, Any, Optional, Callable
from contextlib import asynccontextmanager
from functools import wraps
import time

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from mcp.server.fastmcp import FastMCP

# Import shared tools from the common module
from gaia.gaia_ceto.proto_mcp.mcp_tools import (
    echostring,
    echostring_table,
    long_task,
    firecrawl_scrape_text_only,
)

# Import multi-MCP client with better error handling
try:
    from mcp_client_multi import MultiMCPClient
except ImportError as e:
    # Try alternative import paths
    try:
        from .mcp_client_multi import MultiMCPClient
    except ImportError:
        logging.error(f"Failed to import multi_mcp_client_clean: {e}")
        logging.error("Please ensure multi_mcp_client_clean.py is in the same directory or PYTHONPATH")
        sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MCPServerMulti:
    """Gaia Aggregate MCP server with local and third-party tool support."""

    def __init__(self, config_file: str = "server_config.json"):
        self.config_file = config_file
        self.third_party_client: Optional[MultiMCPClient] = None
        self.server_configs: Dict[str, Dict[str, Any]] = {}
        self.global_settings: Dict[str, Any] = {}
        self._initialized = False
        self._cleanup_lock = asyncio.Lock()  # Create immediately, not in lifespan  # Initialize as None, create in async context
        self._connection_retry_counts: Dict[str, int] = {}
        self._max_retries = 3
        self._retry_delay = 5.0
        
        # Initialize FastMCP with proper lifespan management
        self.mcp = FastMCP("gaia_aggregate_server", lifespan=self.lifespan)
        
        # Register built-in tools immediately
        self._register_builtin_tools()

    @asynccontextmanager
    async def lifespan(self, app):
        """Lifespan context manager for proper async resource management."""
        try:
            # Startup - create async resources in the correct event loop
            logger.info("Starting up MCP server...")
            try:
                await self._safe_initialize()
                initialized = True
            except Exception as e:
                logger.error(f"Initialization failed: {e}")
                # Only cleanup if we partially initialized
                if self.third_party_client is not None:
                    await self._safe_cleanup()
                raise
            yield
        finally:
            # Shutdown - only cleanup if we initialized
            if initialized:
                logger.info("Shutting down MCP server...")
                await self._safe_cleanup()
    
    async def _safe_initialize(self):
        """Initialize with proper error handling."""
        try:
            if not self._initialized:
                self.third_party_client = MultiMCPClient()
                await self.initialize()
                self._initialized = True
        except Exception as e:
            logger.error(f"Failed to initialize server: {e}")
            logger.error(traceback.format_exc())
            # Don't re-raise - allow server to start with limited functionality
    
    async def _safe_cleanup(self):
        """Cleanup with proper error handling and locking."""
        if self._cleanup_lock is None:
            logger.warning("Cleanup lock not initialized, creating temporary lock")
            self._cleanup_lock = asyncio.Lock()

        async with self._cleanup_lock:
            try:
                await self.cleanup()
            except Exception as e:
                logger.error(f"Error during cleanup: {e}")
                logger.error(traceback.format_exc())
    
    def _register_builtin_tools(self):
        """Register built-in local tools."""
        # Register shared tools from mcp_tools module
        self.mcp.add_tool(echostring)
        self.mcp.add_tool(echostring_table)
        self.mcp.add_tool(long_task)
        self.mcp.add_tool(firecrawl_scrape_text_only)

        @self.mcp.tool()
        async def echo(message: str) -> str:
            """Echo a message back to test the server."""
            return f"Echo: {message}"

        @self.mcp.tool()
        async def server_info() -> str:
            """Get comprehensive server information."""
            info = ["Gaia Aggregate Server - Information", "=" * 40, ""]
            info.append(f"Config file: {self.config_file}")
            info.append(f"Third-party servers configured: {len(self.server_configs)}")
            info.append(f"Initialized: {self._initialized}")
            
            # List configured servers
            if self.server_configs:
                info.append("\nConfigured servers:")
                for server_id, config in self.server_configs.items():
                    enabled = "✅" if config.get('enabled', True) else "❌"
                    server_type = "URL" if 'url' in config else "Process"
                    protocol = config.get('protocol', 'N/A')
                    namespace = config.get('namespace', server_id)
                    retry_count = self._connection_retry_counts.get(server_id, 0)
                    retry_info = f" (retries: {retry_count})" if retry_count > 0 else ""
                    info.append(f"  {enabled} {server_id} ({server_type}/{protocol}) -> {namespace}__*{retry_info}")
            
            # List active connections
            if self.third_party_client and hasattr(self.third_party_client, 'connections'):
                active_connections = [k for k, v in self.third_party_client.connections.items() if v]
                info.append(f"\nActive connections: {len(active_connections)}")
                for conn_id in active_connections:
                    connection = self.third_party_client.connections.get(conn_id, {})
                    tool_count = len(connection.get('tools', []))
                    info.append(f"  ✅ {conn_id} ({tool_count} tools)")
            
            return "\n".join(info)
        
        @self.mcp.tool()
        async def list_all_tools() -> str:
            """List all available tools (built-in and third-party)."""
            tools = ["Available Tools", "=" * 20, ""]
            
            # Built-in tools
            tools.append("Built-in tools:")
            builtin_tools = [
                "echostring",
                "echostring_table", 
                "long_task",
                "firecrawl_scrape_text_only",
                "echo",
                "server_info",
                "list_all_tools"
            ]
            for tool in builtin_tools:
                tools.append(f"  • {tool}")
            
            # Third-party tools
            if self.third_party_client and hasattr(self.third_party_client, 'connections'):
                for server_id, connection in self.third_party_client.connections.items():
                    if connection and connection.get('tools'):
                        namespace = self.server_configs.get(server_id, {}).get('namespace', server_id)
                        tools.append(f"\n{server_id} tools (namespace: {namespace}):")
                        for tool in connection['tools']:
                            tools.append(f"  • {namespace}__{tool['name']}")
            
            return "\n".join(tools)
    
    async def load_configuration(self):
        """Load and validate server configuration."""
        if not os.path.exists(self.config_file):
            logger.warning(f"Configuration file {self.config_file} not found - running with built-in tools only")
            return
        
        try:
            with open(self.config_file, 'r') as f:
                config = json.load(f)

            self.server_configs = config.get('mcpServers', {})
            self.global_settings = config.get('globalSettings', {
                'defaultTimeout': 600,
                'longRunningToolTimeout': 600,
                'maxRetries': 3,
                'retryDelay': 5.0
            })
            
            # Update retry settings
            self._max_retries = self.global_settings.get('maxRetries', 3)
            self._retry_delay = self.global_settings.get('retryDelay', 5.0)
            
            # Validate configuration
            for server_id, server_config in self.server_configs.items():
                if 'url' not in server_config and 'command' not in server_config:
                    logger.warning(f"Server {server_id} missing 'url' or 'command' - skipping")
                    server_config['enabled'] = False
            
            enabled_count = sum(1 for cfg in self.server_configs.values() if cfg.get('enabled', True))
            logger.info(f"Loaded configuration: {enabled_count}/{len(self.server_configs)} servers enabled")
            logger.info(f"Global timeout settings: default={self.global_settings.get('defaultTimeout')}s, "
                       f"long-running={self.global_settings.get('longRunningToolTimeout')}s")
            logger.info(f"Retry settings: max_retries={self._max_retries}, retry_delay={self._retry_delay}s")

        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse configuration file: {e}")
            self.server_configs = {}
            self.global_settings = {'defaultTimeout': 120, 'longRunningToolTimeout': 300}
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            logger.error(traceback.format_exc())
            self.server_configs = {}
            self.global_settings = {'defaultTimeout': 120, 'longRunningToolTimeout': 300}

    def get_tool_timeout(self, server_id: str, tool_name: str) -> float:
        """Get the appropriate timeout for a specific tool."""
        # Check for tool-specific timeout in server config
        server_config = self.server_configs.get(server_id, {})
        tool_timeouts = server_config.get('toolTimeouts', {})

        if tool_name in tool_timeouts:
            return float(tool_timeouts[tool_name])

        # Check for server-level timeout
        if 'timeout' in server_config:
            return float(server_config['timeout'])

        # Check if this is a long-running tool
        long_running_patterns = [
            'firecrawl_', 'crawl', 'scrape', 'search', 'fetch', 'download'
        ]
        
        if any(pattern in tool_name.lower() for pattern in long_running_patterns):
            return float(self.global_settings.get('longRunningToolTimeout', 300))

        # Default timeout
        return float(self.global_settings.get('defaultTimeout', 120))

    async def connect_third_party_servers(self):
        """Connect to all enabled third-party servers."""
        if not self.server_configs:
            logger.info("No third-party servers configured")
            return
        
        if not self.third_party_client:
            logger.error("Third-party client not initialized")
            return
        
        # Connect to servers concurrently with proper error handling
        tasks = []
        for server_id, config in self.server_configs.items():
            if config.get('enabled', True):
                tasks.append(self._connect_and_register_server_with_retry(server_id, config))
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            success_count = sum(1 for r in results if r is True)
            logger.info(f"Third-party servers: {success_count}/{len(tasks)} connected successfully")
    
    async def _connect_and_register_server_with_retry(self, server_id: str, config: Dict[str, Any]) -> bool:
        """Connect to a server with retry logic."""
        for attempt in range(self._max_retries):
            try:
                if await self._connect_and_register_server(server_id, config):
                    self._connection_retry_counts[server_id] = attempt
                    return True
                
                if attempt < self._max_retries - 1:
                    logger.warning(f"Connection attempt {attempt + 1}/{self._max_retries} failed for {server_id}, retrying in {self._retry_delay}s...")
                    await asyncio.sleep(self._retry_delay)
                    
            except Exception as e:
                logger.error(f"Error connecting to {server_id} (attempt {attempt + 1}/{self._max_retries}): {e}")
                if attempt < self._max_retries - 1:
                    await asyncio.sleep(self._retry_delay)
        
        logger.error(f"Failed to connect to {server_id} after {self._max_retries} attempts")
        self._connection_retry_counts[server_id] = self._max_retries
        return False
    
    async def _connect_and_register_server(self, server_id: str, config: Dict[str, Any]) -> bool:
        """Connect to a server and register its tools."""
        try:
            if await self._connect_single_server(server_id, config):
                await self._register_server_tools(server_id, config)
                logger.info(f"✅ Connected to {server_id}")
                return True
            else:
                logger.error(f"❌ Failed to connect to {server_id}")
                return False
        except Exception as e:
            logger.error(f"❌ Error with {server_id}: {e}")
            logger.error(traceback.format_exc())
            return False
    
    async def _connect_single_server(self, server_id: str, config: Dict[str, Any]) -> bool:
        """Connect to a single third-party server."""
        if not self.third_party_client:
            return False
        
        try:
            if 'url' in config:
                # URL-based server (hosted)
                url = self._resolve_env_variables(config['url'])
                protocol = config.get('protocol', 'sse')
                description = config.get('description', f'{server_id} hosted server')
                
                return await self.third_party_client.add_server(
                    server_id=server_id,
                    server_url=url,
                    protocol=protocol,
                    description=description
                )
            
            elif 'command' in config:
                # Process-spawned server
                command = config['command']
                args = config.get('args', [])
                env = {k: self._resolve_env_variables(v) for k, v in config.get('env', {}).items()}
                description = config.get('description', f'{server_id} process server')
                
                return await self.third_party_client.add_server_process(
                    server_id=server_id,
                    command=command,
                    args=args,
                    env=env,
                    description=description
                )
            
            else:
                logger.error(f"Invalid configuration for {server_id}: missing 'url' or 'command'")
                return False
                
        except Exception as e:
            logger.error(f"Failed to connect to {server_id}: {e}")
            return False
    
    def _resolve_env_variables(self, value: str) -> str:
        """Resolve environment variables in configuration values."""
        if isinstance(value, str) and '{' in value and '}' in value:
            try:
                # Use safe substitution to avoid KeyError
                import string
                template = string.Template(value.replace('{', '$').replace('}', ''))
                return template.safe_substitute(os.environ)
            except Exception as e:
                logger.warning(f"Failed to resolve environment variables in '{value}': {e}")
                return value
        return value
    
    async def _register_server_tools(self, server_id: str, config: Dict[str, Any]):
        """Register all tools from a connected third-party server."""
        if not self.third_party_client:
            return
            
        connection = self.third_party_client.connections.get(server_id)
        if not connection or not connection.get('tools'):
            logger.warning(f"No tools found for {server_id}")
            return
        
        namespace = config.get('namespace', server_id)
        param_mappings = config.get('parameterMapping', {})
        default_params = config.get('defaultParameters', {})
        
        registered_count = 0
        for tool_info in connection['tools']:
            try:
                tool_name = tool_info['name']
                namespaced_name = f"{namespace}__{tool_name}"
                
                # Create delegated tool with proper closure
                delegated_tool = self._create_delegated_tool(
                    server_id, tool_name, namespaced_name, tool_info,
                    param_mappings.get(tool_name, {}),
                    default_params.get(tool_name, {})
                )
                
                # Register with FastMCP
                self.mcp.tool()(delegated_tool)
                registered_count += 1
                logger.debug(f"Registered: {namespaced_name}")
                
            except Exception as e:
                logger.error(f"Failed to register tool {tool_name} from {server_id}: {e}")
        
        logger.info(f"Registered {registered_count}/{len(connection['tools'])} tools from {server_id} with namespace '{namespace}'")
    
    def _create_delegated_tool(self, server_id: str, tool_name: str, namespaced_name: str, 
                              tool_info: Dict[str, Any], param_mapping: Dict[str, str], 
                              default_params: Dict[str, Any]) -> Callable:
        """Create a delegated tool function with proper parameter handling and retry logic."""
        
        async def delegated_tool(**kwargs) -> Any:
            """Delegated tool implementation with retry logic."""
            logger.debug(f"Delegated tool {namespaced_name} called with kwargs: {kwargs}")

            # Handle parameter unwrapping safely
            actual_params = self._unwrap_parameters(kwargs)
            logger.debug(f"Unwrapped parameters for {namespaced_name}: {actual_params}")

            # Apply parameter mapping and defaults
            final_params = default_params.copy()
            for param_name, param_value in actual_params.items():
                mapped_name = param_mapping.get(param_name, param_name)
                final_params[mapped_name] = param_value

            logger.debug(f"Final params for {namespaced_name}: {final_params}")

            # Call third-party tool with improved error handling and retry logic
            if not self.third_party_client:
                raise RuntimeError("Third-party client not initialized")

            # Get appropriate timeout for this tool
            timeout_seconds = self.get_tool_timeout(server_id, tool_name)
            logger.debug(f"Using timeout of {timeout_seconds}s for {tool_name}")

            last_error = "No attempts made"  # Initialize to prevent UnboundLocalError
            for attempt in range(self._max_retries):
                try:
                    logger.info(f"Calling {tool_name} on {server_id} (attempt {attempt + 1}/{self._max_retries})")

                    # Create task with proper context
                    result = await asyncio.wait_for(
                        self.third_party_client.call_tool(
                            server_id=server_id,
                            tool_name=tool_name,
                            tool_input=final_params,
                            tool_call_id=f"call_{server_id}_{tool_name}_{id(kwargs)}_{attempt}"
                        ),
                        timeout=timeout_seconds
                    )

                    logger.info(f"Tool {tool_name} completed: success={result.get('success', False)}")

                    if result.get('success'):
                        content = result.get('content', [])
                        formatted_result = self._format_tool_result(content)
                        logger.debug(f"Formatted result length: {len(formatted_result)} chars")
                        return formatted_result
                    else:
                        error_msg = result.get('error', 'Unknown error')
                        logger.error(f"Tool {tool_name} reported failure: {error_msg}")
                        
                        # Check if this is a retryable error
                        if self._is_retryable_error(error_msg) and attempt < self._max_retries - 1:
                            logger.warning(f"Retryable error, waiting {self._retry_delay}s before retry...")
                            await asyncio.sleep(self._retry_delay)
                            continue
                        
                        raise RuntimeError(f"Tool {tool_name} failed: {error_msg}")

                except asyncio.TimeoutError as e:
                    last_error = f"Tool {namespaced_name} timed out after {timeout_seconds} seconds"
                    logger.error(last_error)
                    
                    if attempt < self._max_retries - 1:
                        logger.warning(f"Timeout occurred, retrying in {self._retry_delay}s...")
                        await asyncio.sleep(self._retry_delay)
                        continue
                    
                    raise TimeoutError(last_error)
                    
                except asyncio.CancelledError:
                    error_msg = f"Tool {namespaced_name} was cancelled"
                    logger.error(error_msg)
                    # Clean up any partial state before re-raising
                    last_error = error_msg
                    raise
                    
                except Exception as e:
                    last_error = str(e)
                    logger.error(f"Error calling {namespaced_name}: {e}")
                    logger.error(f"Exception type: {type(e).__name__}")
                    
                    # Check if this is a connection error that might be retryable
                    if self._is_retryable_error(str(e)) and attempt < self._max_retries - 1:
                        logger.warning(f"Retryable error, waiting {self._retry_delay}s before retry...")
                        await asyncio.sleep(self._retry_delay)
                        
                        # Try to reconnect to the server with timeout
                        logger.info(f"Attempting to reconnect to {server_id}...")
                        config = self.server_configs.get(server_id, {})
                        if config:
                            try:
                                reconnected = await asyncio.wait_for(
                                    self._connect_single_server(server_id, config),
                                    timeout=30.0  # 30 second reconnection timeout
                                )
                                if reconnected and self.third_party_client.connections.get(server_id):
                                    logger.info(f"Reconnected to {server_id}, retrying tool call...")
                                    continue
                                else:
                                    logger.warning(f"Reconnection to {server_id} failed or connection invalid")
                            except asyncio.TimeoutError:
                                logger.warning(f"Reconnection to {server_id} timed out after 30s")
                    
                    if attempt == self._max_retries - 1:
                        logger.error(traceback.format_exc())
                        raise RuntimeError(f"Failed to call {namespaced_name} after {self._max_retries} attempts: {last_error}") from e
            
            # Should not reach here, but just in case
            raise RuntimeError(f"Failed to call {namespaced_name}: {last_error or 'Unknown error after all retries'}")
        
        # Set function metadata
        delegated_tool.__name__ = namespaced_name
        delegated_tool.__doc__ = tool_info.get('description', f'Third-party tool: {tool_name}')
        
        # Preserve any annotations from tool_info
        if 'inputSchema' in tool_info:
            delegated_tool.__annotations__ = self._extract_annotations(tool_info['inputSchema'])
        
        return delegated_tool
    
    def _is_retryable_error(self, error_msg: str) -> bool:
        """Check if an error is retryable."""
        retryable_patterns = [
            'connection',
            'closed',
            'timeout',
            'incomplete',
            'peer',
            'reset',
            'broken pipe',
            'eof',
            'stream',
            'sse'
        ]
        
        error_lower = error_msg.lower()
        return any(pattern in error_lower for pattern in retryable_patterns)
    
    def _unwrap_parameters(self, kwargs: Dict[str, Any]) -> Dict[str, Any]:
        """Safely unwrap parameters from various formats."""
        # Handle FastMCP parameter wrapping
        if len(kwargs) == 1 and 'kwargs' in kwargs:
            return kwargs['kwargs']
        
        # Handle nested kwargs structure
        if 'kwargs' in kwargs and isinstance(kwargs['kwargs'], dict):
            # Merge nested kwargs with top-level
            result = kwargs.copy()
            nested = result.pop('kwargs')
            result.update(nested)
            return result
        
        return kwargs
    
    def _extract_annotations(self, input_schema: Dict[str, Any]) -> Dict[str, type]:
        """Extract type annotations from JSON schema."""
        annotations = {}
        
        if 'properties' in input_schema:
            for prop_name, prop_schema in input_schema['properties'].items():
                prop_type = prop_schema.get('type', 'string')
                
                # Map JSON schema types to Python types
                type_mapping = {
                    'string': str,
                    'number': float,
                    'integer': int,
                    'boolean': bool,
                    'array': list,
                    'object': dict
                }
                
                annotations[prop_name] = type_mapping.get(prop_type, Any)
        
        return annotations
    
    def _format_tool_result(self, content: List[Any]) -> str:
        """Format tool result content for display."""
        if not content:
            return "No content returned"

        # Handle single text content
        if len(content) == 1:
            return self._format_single_content(content[0])

        # Handle multiple content items
        result_parts = []
        for item in content:
            formatted = self._format_single_content(item)
            if formatted:
                result_parts.append(formatted)

        return "\n\n".join(result_parts) if result_parts else "No content returned"
    
    def _format_single_content(self, item: Any) -> str:
        """Format a single content item."""
        if hasattr(item, 'text'):
            return str(item.text)
        elif hasattr(item, 'data'):
            return str(item.data)
        elif isinstance(item, str):
            return item
        elif isinstance(item, dict):
            # Pretty-print dictionaries
            try:
                return json.dumps(item, indent=2, ensure_ascii=False)
            except (TypeError, ValueError):
                return str(item)
        elif isinstance(item, (list, tuple)):
            # Format sequences
            try:
                return json.dumps(item, indent=2, ensure_ascii=False)
            except (TypeError, ValueError):
                return str(item)
        else:
            # Fallback to string representation
            return str(item)
    
    async def initialize(self):
        """Initialize the complete server."""
        logger.info("Initializing Gaia Aggregate Server...")
        
        try:
            await self.load_configuration()
            await self.connect_third_party_servers()
            logger.info("Gaia Aggregate Server initialization completed successfully")
        except Exception as e:
            logger.error(f"Error during initialization: {e}")
            logger.error(traceback.format_exc())
            # Don't re-raise - server can still function with built-in tools
    
    async def cleanup(self):
        """Clean up all resources."""
        logger.info("Cleaning up Gaia Aggregate Server...")
        
        if self.third_party_client:
            try:
                await self.third_party_client.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning up third-party client: {e}")
        
        self._initialized = False
        logger.info("Cleanup complete")
    
    async def run_server(self, host: str = "0.0.0.0", port: int = 9000):
        """Run the complete server with proper async lifecycle."""
        logger.info(f"Starting Gaia Aggregate Server on {host}:{port}")

        try:
            # Create and run the server - initialization handled by lifespan
            app = self.mcp.streamable_http_app()

            import uvicorn
            config = uvicorn.Config(
                app=app,
                host=host,
                port=port,
                log_level="info",
                access_log=False,
                loop="asyncio"  # Explicitly use asyncio
            )

            server = uvicorn.Server(config)
            await server.serve()

        except Exception as e:
            logger.error(f"Server error: {e}")
            logger.error(traceback.format_exc())
            raise


def main():
    """Main entry point with argument parsing."""
    parser = argparse.ArgumentParser(description="Gaia Aggregate MCP HTTP Server")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to (default: 0.0.0.0)")
    parser.add_argument("--port", type=int, default=9000, help="Port to bind to (default: 9000)")
    parser.add_argument("--config", default="server_config.json", help="Configuration file (default: server_config.json)")
    parser.add_argument("--debug", action="store_true", help="Enable debug logging")
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Create and run server
    server = MCPServerMulti(args.config)
    
    try:
        asyncio.run(server.run_server(args.host, args.port))
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server failed: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()